from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from datetime import datetime, UTC
from models.base import User, MemberOrder
from common.log import logger
from common.globals_enums import OrderStatusEnum
import os
import tempfile
import atexit
from common.utils import get_shanghai_current_time
from datetime import timedelta

# Create scheduler instance
scheduler = AsyncIOScheduler()
LOCK_FILE = os.path.join(tempfile.gettempdir(), "scheduler.lock")


def check_lock():
    """
    Check if scheduler is already running
    Returns True if this is the first instance
    """
    if os.path.exists(LOCK_FILE):
        # Check if the process holding the lock is still running
        try:
            with open(LOCK_FILE, "r") as f:
                pid = int(f.read().strip())
            # Try to check if process is running
            os.kill(pid, 0)
            return False
        except (OSError, ValueError):
            # Process not running or invalid pid
            pass

    # Create lock file with current process id
    with open(LOCK_FILE, "w") as f:
        f.write(str(os.getpid()))
    return True


def cleanup_lock():
    """Remove the lock file on process exit"""
    try:
        os.remove(LOCK_FILE)
    except OSError:
        pass


async def check_membership_expiration():
    """
    Check and update user membership status
    Runs at 3 AM every day
    """
    try:
        now = get_shanghai_current_time()
        # Get all users who are currently members and whose membership has expired
        expired_users = await User.filter(is_member=True, member_expired_time__lt=now)

        # Update their membership status
        for user in expired_users:
            user.is_member = False
            await user.save()

        logger.info(
            f"Membership check completed. {len(expired_users)} users' memberships expired."
        )
    except Exception as e:
        logger.error(f"Error in membership check job: {e}")


# 检查过期的订单
async def check_expired_orders():
    """
    Check and update expired orders
    Runs at 3 AM every day
    """
    try:
        now = get_shanghai_current_time() + timedelta(minutes=30)
        # Get all orders that are expired
        expired_orders = await MemberOrder.filter(
            create_time__lt=now,
            status=OrderStatusEnum.PENDING,
            pay_time__isnull=True,
        )
        for order in expired_orders:
            order.status = OrderStatusEnum.CANCELLED
            await order.save()
        logger.info(
            f"Expired orders check completed. {len(expired_orders)} orders expired. {now}"
        )
    except Exception as e:
        logger.error(f"Error in expired orders check job: {e}")


def init_scheduler():
    """
    Initialize the scheduler with jobs
    """
    # Only start scheduler if this is the first instance
    if not check_lock():
        logger.info("Scheduler is already running in another worker")
        return

    # Register cleanup function
    atexit.register(cleanup_lock)

    # Add membership check job - runs at 3 AM every day
    # scheduler.add_job(
    #     check_membership_expiration,
    #     trigger=CronTrigger(hour=3, minute=0),
    #     id='check_membership_expiration',
    #     name='Check membership expiration status',
    #     replace_existing=True
    # )
    # 实现每一小时执行一次
    scheduler.add_job(
        check_membership_expiration,
        trigger=CronTrigger(hour="*"),
        id="check_membership_expiration",
        name="Check membership expiration status",
        replace_existing=True,
    )
    # 每天晚上12点执行 检查过期的订单
    scheduler.add_job(
        check_expired_orders,
        trigger=CronTrigger(hour=0, minute=0),
        id="check_expired_orders",
        name="Check expired orders",
        replace_existing=True,
    )
    # Start the scheduler
    scheduler.start()
    logger.info("Scheduler started successfully in worker process")
