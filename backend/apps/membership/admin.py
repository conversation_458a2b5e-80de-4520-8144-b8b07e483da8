from fastapi import APIRouter, Depends
from core.security import verify_admin_user
from .curd import MemberOrderCRUD, MembershipPlanCRUD, MembershipCRUD
from .schema import (
    MemberOrderList,
    MemberOrderWithPlan,
    MembershipPlanCreate,
    MembershipPlanUpdate,
    MembershipPlanOut,
    MembershipPlanList,
    MembershipPlanInfo,
    GrantMembershipDuration,
)
from core.base_response import resp_200
from core.base_schemas import BaseOut
from core.error_enums.error import ObjectNotExist
from fastapi import Query

router = APIRouter(prefix="/admin", tags=["admin"])


# 订单列表
@router.get(
    "/orders",
    response_model=MemberOrderList,
    description="订单列表",
    dependencies=[Depends(verify_admin_user)],
)
async def order_list(page: int = 1, page_size: int = 20):
    """
    订单列表
    """
    data_list, total = await MemberOrderCRUD.get_order_list(page, page_size)
    data_list = [
        MemberOrderWithPlan.model_validate(order, strict=False) for order in data_list
    ]
    return resp_200(data=data_list, total=total, page=page, page_size=page_size)


@router.post(
    "/plans", response_model=BaseOut, dependencies=[Depends(verify_admin_user)]
)
async def create_membership_plan(
    plan_data: MembershipPlanCreate,  # type: ignore
):
    """Create a new membership plan"""
    plan = await MembershipPlanCRUD.create(plan_data)
    return resp_200(data=plan)


@router.get(
    "/plans/{plan_id}",
    response_model=MembershipPlanOut,
    dependencies=[Depends(verify_admin_user)],
)
async def get_membership_plan(
    plan_id: int,
):
    """Get a membership plan by ID"""
    plan = await MembershipPlanCRUD.get_obj_by_id(plan_id)
    return resp_200(data=plan)


@router.get(
    "/plans",
    response_model=MembershipPlanList,
    dependencies=[Depends(verify_admin_user)],
)
async def list_membership_plans(
    page: int = Query(1, gt=0),
    page_size: int = Query(20, gt=0),
    search: str = Query(None, description="搜索关键字"),
):
    """Get list of membership plans"""
    plans, total = await MembershipPlanCRUD.get_list(page, page_size, search)
    return resp_200(data=plans, total=total, page=page, page_size=page_size)


@router.put(
    "/plans/{plan_id}",
    response_model=BaseOut,
    dependencies=[Depends(verify_admin_user)],
)
async def update_membership_plan(
    plan_id: int,
    plan_data: MembershipPlanUpdate,  # type: ignore
):
    """Update a membership plan"""
    updated_plan = await MembershipPlanCRUD.update(plan_id, plan_data)
    return resp_200(data=updated_plan)


@router.delete(
    "/plans/{plan_id}",
    response_model=BaseOut,
    dependencies=[Depends(verify_admin_user)],
)
async def delete_membership_plan(
    plan_id: int,
):
    await MembershipPlanCRUD.delete(plan_id)
    return resp_200()


# 管理员收到赠送用户会员时长
@router.post(
    "/membership/grant",
    response_model=BaseOut,
    dependencies=[Depends(verify_admin_user)],
)
async def grant_membership_duration(datas: GrantMembershipDuration):
    """
    管理员收到赠送用户会员时长
    """
    await MembershipCRUD.grant_membership_duration(datas.user_id, datas.plan_id)
    return resp_200()


# 获取所有会员计划
@router.get(
    "/allplans",
    response_model=BaseOut,
    dependencies=[Depends(verify_admin_user)],
)
async def get_all_plans():
    """Get all membership plans"""
    plans = await MembershipPlanCRUD.get_all_plans()
    return resp_200(
        data=[MembershipPlanInfo.model_validate(plan, strict=False) for plan in plans]
    )
