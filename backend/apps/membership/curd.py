from datetime import datetime, timedelta, UTC
from tortoise.transactions import atomic
from models.base import User, MembershipPlan, MemberOrder, OrderStatusEnum, InviteRecord
from common.order_utils import OrderNumberGenerator
from common.utils import get_shanghai_current_time
from core.error_enums.error import TrialAlreadyUsed, ObjectNotExist, PaymentQRCodeError
import secrets
import string
from core.base_curd import BaseCURD
from models.base import MembershipPlan
from .schema import MembershipPlanCreate, MembershipPlanUpdate
from tortoise.expressions import F


from .utils import PaymentUtils, WxPayParams
from common.log import logger


class MembershipCRUD:
    """
    Membership management operations
    """

    @staticmethod
    @atomic()
    async def activate_trial(user_id: int):
        """Activate trial membership for new user"""
        user = await User.get(id=user_id)
        if user.has_used_trial:
            raise TrialAlreadyUsed
        try:
            # Get the trial plan
            trial_plan = await MembershipPlan.get(name="首次激活赠送")
        except:
            trial_plan, _ = await MembershipPlan.get_or_create(
                name="首次激活赠送",
                defaults={
                    "duration_days": 5,
                    "original_price": 0,
                    "discount_price": 0,
                    "description": "首次激活赠送",
                    "is_active": True,
                },
            )
        # Create order for trial
        order = await MemberOrder.create(
            user=user,
            plan=trial_plan,
            order_no=OrderNumberGenerator.generate_order_no(user_id),
            original_amount=0,
            discount_amount=0,
            actual_amount=0,
            status=OrderStatusEnum.PAID,
            pay_time=get_shanghai_current_time(),
            pay_channel="system",
            duration_days=trial_plan.duration_days,
        )

        now = get_shanghai_current_time()
        user.has_used_trial = True
        if (
            user.is_member
            and user.member_expired_time
            and user.member_expired_time > now
        ):
            # Extend existing membership
            user.member_expired_time += timedelta(days=trial_plan.duration_days)
        else:
            # New membership period
            user.is_member = True
            user.member_created_time = now
            user.member_expired_time = now + timedelta(days=trial_plan.duration_days)

        user.total_member_days += trial_plan.duration_days
        await user.save()

    @staticmethod
    @atomic()
    async def grant_membership_duration(user_id: int, plan_id: int | None = None):
        """Grant 5-day membership reward for invitation"""
        if not plan_id:
            try:
                trial_plan = await MembershipPlan.get(name="邀请奖励")
            except:
                trial_plan, _ = await MembershipPlan.get_or_create(
                    name="邀请奖励",
                    defaults={
                        "duration_days": 7,
                        "original_price": 0,
                        "discount_price": 0,
                        "description": "邀请奖励",
                        "is_active": True,
                    },
                )
        else:
            trial_plan = await MembershipPlan.get(id=plan_id)

        user = await User.get(id=user_id)
        # Create order for invitation reward
        order = await MemberOrder.create(
            user=user,
            plan=trial_plan,
            order_no=OrderNumberGenerator.generate_order_no(user_id),
            original_amount=0,
            discount_amount=0,
            actual_amount=0,
            status=OrderStatusEnum.PAID,
            pay_time=get_shanghai_current_time(),
            pay_channel="system",
            duration_days=trial_plan.duration_days,
        )

        # Update user membership status
        now = get_shanghai_current_time()
        if (
            user.is_member
            and user.member_expired_time
            and user.member_expired_time > now
        ):
            # Extend existing membership
            user.member_expired_time += timedelta(days=trial_plan.duration_days)
        else:
            # New membership period
            user.is_member = True
            user.member_created_time = now
            user.member_expired_time = now + timedelta(days=trial_plan.duration_days)

        user.total_member_days += trial_plan.duration_days
        await user.save()

    @staticmethod
    async def get_order(order_id: int):
        """Get order by id"""
        try:
            return await MemberOrder.get(id=order_id)
        except:
            raise ObjectNotExist


class MemberOrderCRUD:
    """
    Member order management operations
    """

    @staticmethod
    @atomic()
    async def create_order(user_id: int, pay_channel: str, plan_id: int):
        """Create an order for a user"""
        try:
            plan = await MembershipPlan.get(id=plan_id)
        except:
            raise ObjectNotExist
        order_no = OrderNumberGenerator.generate_order_no(user_id)
        order = await MemberOrder.create(
            user_id=user_id,
            plan_id=plan_id,
            order_no=order_no,
            original_amount=plan.original_price,
            discount_amount=plan.discount_price,
            actual_amount=plan.discount_price,
            status=OrderStatusEnum.PENDING,
            pay_time=get_shanghai_current_time(),
            pay_channel=pay_channel,
            duration_days=plan.duration_days,
        )

        # 生成支付二维码
        params = WxPayParams(
            mch_id="1718247984",
            out_trade_no=order_no,
            total_fee=plan.discount_price,
            body=f"{plan.name}",
            notify_url="https://api.xiaobinai.top/api/v1/membership/order/callback",
        )
        try:
            result = await PaymentUtils.generate_wx_pay_qrcode(params)
        except Exception as e:
            logger.error(
                f"生成支付二维码失败: {e}, 参数: {params} 用户id: {user_id} 订单id: {order_no}"
            )
            raise PaymentQRCodeError
        code = result.get("code")
        if code != 0:
            logger.error(f"请求结果: {result}")
            logger.error(
                f"支付二维码生成失败: {result.get('message')}, 参数: {params} 用户id: {user_id} 订单id: {order_no}"
            )
            raise PaymentQRCodeError
        QRcode_url = result.get("data").get("QRcode_url")
        # QRcode_url = "https://www.onlineqrgenerator.com/images/qr-code-0.webp"

        return {"order_id": order.id, "order_no": order_no, "QRcode_url": QRcode_url}

    @staticmethod
    async def get_order_list(page: int, page_size: int = 20, user_id: int = None):
        """Get order list"""
        offset = (page - 1) * page_size
        query_set = (
            MemberOrder.all()
            .annotate(email=F("user__email"))
            .select_related("plan", "user")
        )
        if user_id:
            query_set = query_set.filter(user_id=user_id, status=OrderStatusEnum.PAID)
        total = await query_set.count()
        data_list = await query_set.offset(offset).limit(page_size).order_by("-id")
        return data_list, total

    @staticmethod
    @atomic()
    async def order_call_back(out_trade_no: str, order_no):
        try:
            # 使用 select_for_update() 获取行级锁
            order = await MemberOrder.get(order_no=out_trade_no).select_for_update()
            user = await User.get(id=order.user_id).select_for_update()

            # 更新订单状态
            order.status = OrderStatusEnum.PAID
            order.pay_time = get_shanghai_current_time()
            order.transaction_id = order_no
            await order.save()

            # 获取会员计划
            trial_plan = await order.plan
            now = get_shanghai_current_time()

            # 更新会员时间
            if (
                user.is_member
                and user.member_expired_time
                and user.member_expired_time > now
            ):
                # 如果是有效会员，则在当前过期时间基础上延长
                user.member_expired_time += timedelta(days=trial_plan.duration_days)
            else:
                # 如果不是会员或已过期，从当前时间开始计算
                user.is_member = True
                user.member_created_time = now
                user.member_expired_time = now + timedelta(
                    days=trial_plan.duration_days
                )
            user.is_pay_user = True
            user.total_member_days += trial_plan.duration_days
            await user.save()

        except Exception as e:
            raise ObjectNotExist


class InvitationCRUD:
    """
    Invitation management operations
    """

    @staticmethod
    def generate_invite_code(length=8):
        """Generate a random invitation code"""
        alphabet = string.ascii_uppercase + string.digits
        return "".join(secrets.choice(alphabet) for _ in range(length))

    @staticmethod
    async def create_invite_code(user_id: int):
        """Create and assign invitation code to user"""
        user = await User.get(id=user_id)
        if not user.invite_code:
            while True:
                invite_code = InvitationCRUD.generate_invite_code()
                if not await User.filter(invite_code=invite_code).exists():
                    user.invite_code = invite_code
                    await user.save()
                    break
        return user.invite_code

    @staticmethod
    @atomic()
    async def process_invitation(user_id: int, invite_code: str):
        """Process invitation for a new user"""
        invitee = await User.get(id=user_id)
        try:
            inviter = await User.get(invite_code=invite_code)
            if not inviter.is_pay_user:
                raise ValueError("正式会员，才能邀请")
        except:
            raise ValueError("Invalid invitation code")

        if inviter.id == invitee.id:
            raise ValueError("Cannot use own invitation code")

        # Create invitation record
        await InviteRecord.create(
            inviter=inviter,
            invitee=invitee,
            reward_claimed=True,
            reward_time=get_shanghai_current_time(),
        )

        # Update invitee's invited_by field
        invitee.invited_by = inviter
        await invitee.save()

        # Grant rewards to both inviter and invitee
        await MembershipCRUD.grant_membership_duration(inviter.id)
        await MembershipCRUD.grant_membership_duration(invitee.id)


class MembershipPlanCRUD(BaseCURD):
    """
    Membership plan management operations using BaseCURD
    """

    model = MembershipPlan

    @classmethod
    async def get_list(cls, page: int, page_size: int = 20, search: str = None):
        """Get list of membership plans with pagination"""
        offset = (page - 1) * page_size
        query_set = cls.model.all().order_by("-id")
        if search:
            query_set = query_set.filter(name__icontains=search)
        total = await query_set.count()
        data_list = await query_set.offset(offset).limit(page_size).order_by("-id")
        return data_list, total

    @classmethod
    async def get_plans(cls):
        """Get membership plan by name"""
        names = ["月卡", "季卡", "年卡"]
        return await cls.model.filter(is_active=True, name__in=names).order_by("id")

    @classmethod
    async def get_all_plans(cls):
        """Get all membership plans"""
        return await cls.model.filter(is_active=True).order_by("id")
