#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : curd.py
from datetime import timedelta, UTC, datetime
import uuid
from typing import List, Union

from fastapi.encoders import jsonable_encoder
from tortoise.exceptions import DoesNotExist, MultipleObjectsReturned
from tortoise.queryset import QuerySet
from common.utils import get_shanghai_current_time
from common.log import logger

from conf import settings
from core.error_enums.error import (
    UserHasExist,
    ObjectNotExist,
    UserPasswordError,
    MachineCodeLimit,
)
from core.security import create_access_token, get_password_hash, verify_password
from tortoise.exceptions import DoesNotExist
from tortoise.transactions import atomic

from models.base import User

from .schema import *
from apps.membership.curd import MembershipCRUD, InvitationCRUD


class AuthenticationCRUD:
    """
    认证
    """

    @staticmethod
    async def login(user_req: UserLoginRequest, role: str) -> dict:
        try:
            user_obj = await User.get(email=user_req.email, role=role)
        except (DoesNotExist, MultipleObjectsReturned):
            raise ObjectNotExist

        if not verify_password(user_req.password, user_obj.password):
            raise UserPasswordError
        user_obj.last_login = get_shanghai_current_time()
        # 判断会员是否过期
        now = get_shanghai_current_time()
        if (
            user_obj.is_member
            and user_obj.member_expired_time
            and user_obj.member_expired_time < now
        ):
            user_obj.is_member = False
        token = str(uuid.uuid4())
        user_obj.token = token
        await user_obj.save()
        access_token_expires = timedelta(hours=settings.ACCESS_TOKEN_EXPIRE_HOURS)
        access_token = create_access_token(
            data={
                "email": user_obj.email,
                "token": token,
                "role": user_obj.role,
                "uid": user_obj.id,
                "is_member": user_obj.is_member,
                "member_expired_time": user_obj.member_expired_time.strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
                if user_obj.member_expired_time
                else None,
            },
            expires_delta=access_token_expires,
        )

        email = user_obj.email
        return {
            "access_token": access_token,
            "token_type": "Bearer",
            "email": email,
            "uid": user_obj.id,
        }

    @staticmethod
    @atomic()
    async def register(user_req: UserRegisterRequest) -> dict:
        """
        注册
        """
        # 检查邮箱是否已存在
        if await User.filter(email=user_req.email).exists():
            raise UserHasExist

        # 检查机器码使用次数
        if user_req.machine_code:
            machine_code_count = await User.filter(
                machine_code=user_req.machine_code
            ).count()
            if machine_code_count >= 3:
                raise MachineCodeLimit

        # 创建用户
        obj_in_data = jsonable_encoder(user_req)
        obj_in_data["password"] = get_password_hash(obj_in_data["password"])
        # 删除invite_code 如果存在
        if obj_in_data.get("invite_code"):
            del obj_in_data["invite_code"]
        user = await User.create(**obj_in_data)

        # 为新用户生成邀请码
        await InvitationCRUD.create_invite_code(user.id)

        # 自动激活试用期
        try:
            await MembershipCRUD.activate_trial(user.id)
        except ValueError as e:
            logger.error(f"激活试用期失败 user_id: {user.id} error: {str(e)}")

        # 处理邀请码（如果有）
        if user_req.invite_code:
            try:
                await InvitationCRUD.process_invitation(user.id, user_req.invite_code)
            except ValueError as e:
                # 如果邀请码处理失败，继续处理试用期
                logger.error(f"处理邀请码失败 user_id: {user.id} error: {str(e)}")

        return user


class UserModel:
    """
    用户
    """

    @staticmethod
    async def get_user_list(
        page: int,
        page_size: int = 20,
        search: str = "",
        role: str = "",
        is_active: Union[bool, str] = "",
        is_all: Union[bool, str] = False,
    ) -> tuple[QuerySet, int]:
        """
        获取用户列表
        """
        offset = (page - 1) * page_size
        query_set = User.all()
        if search:
            query_set = query_set.filter(email__icontains=search)
        if str(is_all) in ["true", "1", "yes", "on"]:
            total = await query_set.count()
            data_list = await query_set.order_by("-id")
            return data_list, total
        if role:
            query_set = query_set.filter(role=role)
        if is_active != "":
            is_active = is_active.lower() in ["true", "1", "yes", "on"]
            query_set = query_set.filter(is_active=is_active)
        total = await query_set.count()
        data_list = await query_set.offset(offset).limit(page_size).order_by("-id")
        return data_list, total

    @staticmethod
    async def get_user_by_id(user_id):
        try:
            user = await User.get(id=user_id)
        except (DoesNotExist, MultipleObjectsReturned):
            return None
        return user

    @staticmethod
    async def create_user(user: UserCreate):
        """
        新增
        """
        if await User.filter(email=user.email).exists():
            raise UserHasExist
        obj_in_data = jsonable_encoder(user)
        obj_in_data["password"] = get_password_hash(obj_in_data["password"])
        obj = User(**obj_in_data)
        await obj.save()

    @staticmethod
    async def reset_user_password(user_id: int, new_password: str) -> None:
        """
        修改密码
        """
        user = await UserModel.get_user_by_id(user_id)
        if not user:
            raise ObjectNotExist
        user.password = get_password_hash(new_password)
        await user.save()

    @staticmethod
    async def delete_user(user_id: int) -> None:
        """
        删除用户
        """
        user = await UserModel.get_user_by_id(user_id)
        await user.delete()
        # if not user:
        #     raise ObjectNotExist
        # user.is_active = False
        await user.save()

    @staticmethod
    async def update_user(user_id: int, user_obj: UserUpdate) -> None:
        """
        更新用户
        """
        await User.filter(id=user_id).update(**user_obj.model_dump(exclude_unset=True))
