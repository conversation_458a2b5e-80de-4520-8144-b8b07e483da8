#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : views.py
from typing import Optional, Union

from fastapi import APIRouter, Depends, Query

from core.base_response import resp_200
from core.base_schemas import BaseOut
from core.security import verify_admin_user, verify_user_login

from .curd import UserModel
from .schema import (
    UserCreate,
    UserDetail,
    UserList,
    UserRequestResetPassword,
    UserUpdate,
    UserInfo,
    UserLoginRequest,
    UserToken,
)
from common.globals_enums import RoleEnum
from .curd import AuthenticationCRUD

router = APIRouter(prefix="/admin", tags=["admin"])


@router.post("/auth/login", response_model=UserToken, description="登录")
async def user_login(user: UserLoginRequest):
    """
    登录
    """
    token = await AuthenticationCRUD.login(user, RoleEnum.ADMIN)
    return resp_200(data=token)


@router.get(
    "/users",
    response_model=UserList,
    description="用户列表",
    dependencies=[Depends(verify_admin_user)],
)
async def user_list(
    page: int = Query(default=1, description="页码"),
    page_size: int = Query(default=20, description="页码大小"),
    role: str = Query(default="", description="角色"),
    search: str = Query(default="", description="搜索"),
    is_active: Union[bool, str] = Query(default="", description="状态"),
    is_all: Union[bool, str] = Query(default=False, description="是否获取全部"),
):
    query_set, total = await UserModel.get_user_list(
        page,
        page_size=page_size,
        search=search,
        role=role,
        is_active=is_active,
        is_all=is_all,
    )
    return resp_200(
        data=[UserDetail.model_validate(user, strict=False) for user in query_set],
        total=total,
        page=page,
        page_size=page_size,
    )


@router.post(
    "/users",
    response_model=BaseOut,
    description="新增",
    dependencies=[Depends(verify_admin_user)],
)
async def user_create(user: UserCreate):  # type: ignore
    await UserModel.create_user(user)
    return resp_200()


@router.put(
    "/users/reset_password",
    response_model=BaseOut,
    description="修改密码",
    dependencies=[Depends(verify_admin_user)],
)
async def user_reset_password(user_req: UserRequestResetPassword):
    await UserModel.reset_user_password(user_req.user_id, user_req.new_password)
    return resp_200()


@router.delete(
    "/users/{user_id}",
    response_model=BaseOut,
    description="删除",
    dependencies=[Depends(verify_admin_user)],
)
async def user_delete(user_id):
    await UserModel.delete_user(user_id)
    return resp_200()


@router.put(
    "/users/{user_id}",
    response_model=BaseOut,
    description="修改",
    dependencies=[Depends(verify_admin_user)],
)
async def user_update(user_id, user_obj: UserUpdate):
    await UserModel.update_user(user_id, user_obj)
    return resp_200()


@router.get("/users/info", response_model=UserInfo, description="用户信息")
async def user_info(token=Depends(verify_user_login)):
    uid = token.uid
    user = await UserModel.get_user_by_id(uid)
    return resp_200(data=UserDetail.model_validate(user))
