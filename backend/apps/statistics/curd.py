from models.base import *
from datetime import datetime, timedelta
from tortoise.functions import Count, Sum, Function
from common.globals_enums import OrderStatusEnum, RoleEnum
from tortoise.expressions import RawSQL  # 核心修复


class StatisticsCRUD:
    @staticmethod
    async def get_user_count():
        return await User.filter(role=RoleEnum.NORMAL).count()

    @staticmethod
    async def get_member_count():
        return await User.filter(is_member=True).count()

    @staticmethod
    async def get_user_count_by_day():
        # 统计最近七天 每天注册用户的数量
        today = datetime.now().date()
        start_date = today - timedelta(days=6)  # 7天前的日期

        # 构建查询
        result = await (
            User.annotate(date=RawSQL("DATE(create_time)"))  # 使用 Function
            .filter(create_time__gte=start_date)
            .group_by("date")  # Step 3: 按日期分组
            .annotate(count=Count("id"))  # Step 4: 计算每组的数量
            .values("date", "count")
        )

        # 构建完整的7天数据，如果某天没有注册用户，count为0
        stats = {(start_date + timedelta(days=i)): 0 for i in range(7)}

        # 更新实际的统计数据
        for item in result:
            stats[item["date"]] = item["count"]

        # 转换为列表格式返回
        return [
            {"date": date.strftime("%Y-%m-%d"), "count": count}
            for date, count in stats.items()
        ]

    # 获取累计的收入，从订单里面统计
    @staticmethod
    async def get_total_income():
        result = (
            await MemberOrder.filter(status=OrderStatusEnum.PAID)
            .annotate(total=Sum("actual_amount"))
            .values("total")
        )

        # 如果没有收入记录，返回0
        return round(float(result[0]["total"] or 0), 2)
