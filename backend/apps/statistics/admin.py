from fastapi import APIRouter, Depends
from .curd import StatisticsCRUD
from core.base_response import resp_200
from .schema import StatisticsOut, Statistics
from core.security import verify_admin_user


router = APIRouter(prefix="/admin", tags=["admin"])


@router.get(
    "/statistics",
    description="统计接口",
    response_model=StatisticsOut,
    dependencies=[Depends(verify_admin_user)],
)
async def statistics():
    """
    统计接口
    """
    user_count = await StatisticsCRUD.get_user_count()
    member_count = await StatisticsCRUD.get_member_count()
    user_count_by_day = await StatisticsCRUD.get_user_count_by_day()
    total_income = await StatisticsCRUD.get_total_income()

    return resp_200(
        data=Statistics(
            user_count=user_count,
            member_count=member_count,
            user_count_by_day=user_count_by_day,
            total_income=total_income,
        )
    )
