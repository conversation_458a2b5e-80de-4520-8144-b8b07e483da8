<template>
    <div class="table-operations">
        <a-space>
            <a-button type="primary" @click="handleAdd">新增</a-button>
            <a-button type="primary" danger :disabled="!hasSelected" @click="handleBatchDelete">批量删除</a-button>
            <a-input-search addon-before="名称" enter-button @search="onSearch" @change="onSearchChange" />
        </a-space>
    </div>
    <!-- 表格 -->
    <a-table :row-selection="rowSelection" :columns="columns" rowKey="id" :data-source="data" :pagination="pagination"
        :loading="loading" @change="handleTableChange">
        <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'create_time' || column.dataIndex === 'last_login' || column.dataIndex === 'member_expired_time'">
                {{ text ? formatDate(text) : '-' }}
            </template>
            <template v-if="column.dataIndex === 'operation'">
                <span>
                    <a @click="handleGrant(record.id)">赠送</a>
                    <a-divider type="vertical" />
                    <a @click="handleEdit(record)">编辑</a>
                    <a-divider type="vertical" />
                    <a-popconfirm title="确定删除?" ok-text="是" cancel-text="否" @confirm="confirmDelete(record.id)">
                        <a href="#">删除</a>
                    </a-popconfirm>
                </span>
            </template>
        </template>
    </a-table>

    <!--弹窗区域-->
    <div>
        <a-modal :open="modal.visile" :forceRender="true" :title="modal.title" ok-text="确认" cancel-text="取消"
            @cancel="handleCancel" @ok="handleOk">
            <div>
                <a-form ref="myform" :label-col="{ style: { width: '80px' } }" :model="modal.form" :rules="modal.rules">
                    <a-row :gutter="24">
                        <a-col span="24">
                            <a-form-item label="邮箱" name="email">
                                <a-input placeholder="请输入" v-model:value="modal.form.email"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col span="24">
                            <a-form-item label="昵称" name="nickname">
                                <a-input placeholder="请输入" v-model:value="modal.form.nickname"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col span="24">
                            <a-form-item label="平台" name="platform">
                                <a-input placeholder="请输入" v-model:value="modal.form.platform"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col span="24">
                            <a-form-item label="角色" name="role">
                                <a-select placeholder="请选择" allowClear v-model:value="modal.form.role">
                                    <a-select-option key="1" value="1">管理员</a-select-option>
                                    <a-select-option key="2" value="2">普通用户</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col span="24">
                            <a-form-item label="状态" name="is_active">
                                <a-select placeholder="请选择" allowClear v-model:value="modal.form.is_active">
                                    <a-select-option value="true">激活</a-select-option>
                                    <a-select-option value="false">未激活</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col span="24">
                            <a-form-item label="会员状态" name="is_member">
                                <a-select placeholder="请选择" allowClear v-model:value="modal.form.is_member">
                                    <a-select-option value="true">是</a-select-option>
                                    <a-select-option value="false">否</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col span="24">
                            <a-form-item label="邀请码" name="invite_code">
                                <a-input placeholder="请输入" v-model:value="modal.form.invite_code"></a-input>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </a-modal>
    </div>

    <!-- 赠送会员弹窗 -->
    <a-modal :open="grantModal.visile" :title="grantModal.title" ok-text="确认" cancel-text="取消" @cancel="handleCancelGrant" @ok="handleOkGrant">
        <div>
            <a-form ref="myform" :label-col="{ style: { width: '80px' } }" :model="grantModal.form" :rules="grantModal.rules">
                <a-form-item label="会员计划" name="plan_id">
                    <a-select placeholder="请选择" allowClear v-model:value="grantModal.form.plan_id">
                        <a-select-option v-for="item in planList" :key="item.id" :value="item.id">{{ item.name }} {{ item.duration_days }}天 {{ item.discount_price }}元</a-select-option>
                    </a-select>
                </a-form-item>
            </a-form>
        </div>
    </a-modal>

</template>
<script setup>
import { computed, ref, onMounted, reactive, createVNode } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { message, Table, Modal } from 'ant-design-vue';

import { getListAPI, delleteAPI, bathDeleteAPI, updateAPI, createAPI } from '@/apis/admin/user'
import {  getAllPlansAPI, grantMembershipDurationAPI } from '@/apis/admin/member_shep_plan'

import { formatDate } from '@/utils/utils'


const columns = [
    {
        title: 'Id',
        dataIndex: 'id',
        key: 'id',
    },
    {
        title: '邮箱',
        dataIndex: 'email',
        key: 'email',
    },
    // {
    //     title: '昵称',
    //     dataIndex: 'nickname',
    //     key: 'nickname',
    // },
    {
        title: '平台',
        dataIndex: 'platform',
        key: 'platform',
    },
    {
        title: '角色',
        dataIndex: 'role',
        key: 'role',
        customRender: ({ text }) => text === '1' ? '管理员' : '普通用户'
    },
    {
        title: '状态',
        dataIndex: 'is_active',
        key: 'is_active',
        customRender: ({ text }) => text ? '激活' : '未激活'
    },
    {
        title: '会员状态',
        dataIndex: 'is_member',
        key: 'is_member',
        customRender: ({ text }) => text ? '是' : '否'
    },
    {
        title: '会员过期时间',
        dataIndex: 'member_expired_time',
        key: 'member_expired_time',
    },
    {
        title: '最后登录',
        dataIndex: 'last_login',
        key: 'last_login',
    },
    {
        title: '创建时间',
        dataIndex: 'create_time',
        key: 'create_time'
    },
    {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        width: 150
    }
];

const loading = ref(false)

const pagination = ref({
    total: 0,
    current: 1,
    pageSize: 8,
});

const params = ref(
    {
        page: 1,
        search: '',
        page_size: pagination.value.pageSize
    }
)
const data = ref([])

const getList = async () => {
    loading.value = true
    const res = await getListAPI(params.value)
    loading.value = false
    data.value = res.data
    pagination.value.total = res.total

}

const del = (id) => {
    delleteAPI(id).then((res) => {
        if (res.code === 200) {
            data.value = data.value.filter(item => item.id !== id);
        }
    })

}


const handleTableChange = (page, filters, sorter) => {
    console.log(page, filters, sorter)
    pagination.value.current = page.current
    params.value.page = page.current
    pagination.value.pageSize = page.pageSize
    params.value.page_size = page.pageSize
    getList()

};


//  批量删除
const state = reactive({
    selectedRowKeys: []
});


const hasSelected = computed(() => state.selectedRowKeys.length > 0);

const rowSelection = computed(() => {
    return {
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange,
        hideDefaultSelections: true,
        selections: [
            Table.SELECTION_ALL,
            Table.SELECTION_INVERT,
            Table.SELECTION_NONE,
        ],
    };
});

const onSelectChange = selectedRowKeys => {
    console.log('selectedRowKeys changed: ', selectedRowKeys);
    state.selectedRowKeys = selectedRowKeys;
};
const handleBatchDelete = () => {
    console.log(state.selectedRowKeys)
    Modal.confirm({
        title: '是否确认删除？',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确认',
        cancelText: '取消',
        onOk() {
            console.log('OK');
            bathDeleteAPI(state.selectedRowKeys).then((res) => {
                if (res.code === 200) {
                    message.info('删除成功')
                    data.value = data.value.filter(item => !state.selectedRowKeys.includes(item.id));
                    state.selectedRowKeys = []
                }
            }
            )
        },

    });

}

// 搜索
const onSearchChange = (e) => {
    params.value.search = e?.target?.value;
    console.log(params.value.search);
};

const onSearch = () => {
    getList()
};



// 弹窗数据源
const modal = reactive({
    visile: false,
    editFlag: false,
    title: '',
    form: {
        id: undefined,
        email: undefined,
        nickname: undefined,
        platform: undefined,
        role: undefined,
        is_active: undefined,
        is_member: undefined,
        member_expired_time: undefined,
        invite_code: undefined,
        has_used_trial: undefined,
    },
    rules: {
        email: [{ required: true, message: '请输入邮箱', trigger: 'change' }],
        role: [{ required: true, message: '请选择角色', trigger: 'change' }],
    },
});

const myform = ref();

// 恢复表单初始状态
const resetModal = () => {
    myform.value?.resetFields();
};
// 关闭弹窗
const hideModal = () => {
    modal.visile = false;
};
// 取消
const handleCancel = () => {
    hideModal();
};

// 编辑
const handleEdit = (record) => {
    resetModal();
    modal.visile = true;
    modal.editFlag = true;
    modal.title = '编辑';
    // 重置
    for (const key in record) {
        if (Object.keys(modal.form).includes(key)) {
            // Convert boolean values to strings
            if (key === 'is_member' || key === 'is_active') {
                modal.form[key] = record[key].toString();
            } else {
                modal.form[key] = record[key];
            }
        }
    }
};

// 赠送会员
const grantModal = reactive({
    visile: false,
    title: '',
    form: {
        user_id: undefined,
        plan_id: undefined,

    },
    rules: {
        user_id: [{ required: true, message: '请选择用户', trigger: 'change' }],
        plan_id: [{ required: true, message: '请选择会员计划', trigger: 'change' }],
    },
})

const planList = ref([])

const getPlanList = async () => {
    const res = await getAllPlansAPI()
    planList.value = res.data
}

const handleGrant = (id) => {
    grantModal.visile = true
    grantModal.title = '赠送会员'
    grantModal.form.user_id = id
}
const handleCancelGrant = () => {
    grantModal.visile = false
}
const handleOkGrant = async () => {
    myform.value.validate().then(async () => {
        const formData = { ...grantModal.form };
        grantModal.visile = false
        console.log(formData)
        await grantMembershipDurationAPI(formData).then((res) => {
            if (res.code === 200) {
                message.info('赠送成功')
                getList()
            }
            else {
                message.error(res.msg)
            }
        }).catch(error => {
            console.log('error', error);    
        })
    }).catch(error => {
        console.log('error', error);
    });

}

// 新增
const handleAdd = () => {
    resetModal();
    modal.visile = true;
    modal.editFlag = false;
    modal.title = '新增';
};

const handleOk = () => {
    myform.value.validate().then(() => {
        const formData = { ...modal.form };
        // Convert string back to boolean for boolean fields
        if (formData.is_member !== undefined) {
            formData.is_member = formData.is_member === 'true';
        }
        if (formData.is_active !== undefined) {
            formData.is_active = formData.is_active === 'true';
        }
        
        if (modal.editFlag) {
            updateAPI(formData, formData.id).then((res) => {
                hideModal()
                getList()
            })
        } else {
            createAPI(formData).then((res) => {
                hideModal()
                getList()
            }).catch((err) => {
                console.log(err, formData)
            })
        }
    }).catch(error => {
        console.log('error', error);
    });
}

// 删除
const confirmDelete = id => {
    del(id)
};

onMounted(() => {
    getList()
    getPlanList()
})
</script>

<style lang="scss" scoped>
.table-operations {
    margin-bottom: 20px;
}
</style>