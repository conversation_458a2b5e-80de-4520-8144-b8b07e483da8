<template>
  <div class="statistics-container">
    <a-row :gutter="[16, 16]">
      <!-- 统计卡片 -->
      <a-col :span="8">
        <a-card>
          <a-statistic
            title="总用户数"
            :value="statistics.totalUsers"
            :loading="loading"
          >
            <template #suffix>
              <user-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card>
          <a-statistic
            title="会员数"
            :value="statistics.memberUsers"
            :loading="loading"
          >
            <template #suffix>
              <crown-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card>
          <a-statistic
            title="累计收入"
            :value="statistics.totalIncome"
            :precision="2"
            :loading="loading"
            prefix="¥"
          >
            <template #suffix>
              <account-book-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <!-- 用户注册趋势图 -->
      <a-col :span="24">
        <a-card title="近7天用户注册趋势" :loading="loading">
          <v-chart class="chart" :option="userTrendOption" autoresize />
        </a-card>
      </a-col>

      <!-- 会员转化率 -->
      <a-col :span="12">
        <a-card title="会员转化率" :loading="loading">
          <v-chart class="chart" :option="conversionOption" autoresize />
        </a-card>
      </a-col>

      <!-- 数据表格 -->
      <a-col :span="12">
        <a-card title="近7天注册详情" :loading="loading">
          <a-table
            :columns="columns"
            :data-source="userTrendData"
            :pagination="false"
            size="small"
          />
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import {
  UserOutlined,
  CrownOutlined,
  AccountBookOutlined
} from '@ant-design/icons-vue'
import { getStatisticsAPI } from '@/apis/admin/statistics'

// 注册 ECharts 必须的组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const loading = ref(false)
const statistics = ref({
  totalUsers: 0,
  memberUsers: 0,
  totalIncome: 0,
  userTrend: []
})

// 表格列定义
const columns = [
  {
    title: '日期',
    dataIndex: 'date',
    key: 'date'
  },
  {
    title: '注册用户数',
    dataIndex: 'count',
    key: 'count'
  }
]

// 用户趋势图配置
const userTrendOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: statistics.value.userTrend.map(item => item.date),
    boundaryGap: false
  },
  yAxis: {
    type: 'value',
    name: '注册用户数'
  },
  series: [
    {
      name: '注册用户数',
      type: 'line',
      data: statistics.value.userTrend.map(item => item.count),
      smooth: true,
      areaStyle: {
        opacity: 0.3
      }
    }
  ]
}))

// 会员转化率图表配置
const conversionOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)'
  },
  series: [
    {
      type: 'pie',
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'inside',
        formatter: '{d}%'
      },
      data: [
        { 
          value: statistics.value.memberUsers, 
          name: '会员用户',
          itemStyle: { color: '#ffd700' }
        },
        { 
          value: statistics.value.totalUsers - statistics.value.memberUsers,
          name: '普通用户',
          itemStyle: { color: '#e8e8e8' }
        }
      ]
    }
  ]
}))

// 获取统计数据
const fetchStatistics = async () => {
  loading.value = true
  try {
    const res = await getStatisticsAPI()
    if (res?.code === 200) {
      statistics.value = {
        totalUsers: res.data.user_count,
        memberUsers: res.data.member_count,
        totalIncome: res.data.total_income,
        userTrend: res.data.user_count_by_day
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchStatistics()
})

const userTrendData = computed(() => statistics.value.userTrend)
</script>

<style lang="scss" scoped>
.statistics-container {
  padding: 24px;
}
.chart {
  height: 300px;
}
:deep(.ant-card) {
  height: 100%;
}
</style>

