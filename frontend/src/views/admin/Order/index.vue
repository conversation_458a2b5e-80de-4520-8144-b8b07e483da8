<template>
    <div class="order-list">
        <a-table :columns="columns" rowKey="id" :loading="loading" :data-source="data" :pagination="pagination"
            @change="handleTableChange">
            <template #bodyCell="{ column, text, record }">
                <template v-if="column.key === 'amount'">
                    <div>
                        <div>原价：¥{{ record.original_amount }}</div>
                        <div>折扣：¥{{ record.discount_amount }}</div>
                        <div class="actual-amount">实付：¥{{ record.actual_amount }}</div>
                    </div>
                </template>
                <template v-else-if="column.key === 'plan_info'">
                    <div>
                        <div class="plan-name">{{ record.plan.name }}</div>
                        <div class="plan-duration">{{ record.duration_days || record.plan.duration_days }}天</div>
                    </div>
                </template>
                <template v-else-if="column.key === 'status'">
                    <a-tag :color="getStatusColor(text)">{{ getStatusText(text) }}</a-tag>
                </template>
            </template>
        </a-table>
    </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { getMemberOrderListAPI } from '@/apis/admin/member'
import { formatDate } from '@/utils/utils';

// 订单状态
const OrderStatusEnum = {
    PENDING: 'pending',
    PAID: 'paid',
    CANCELLED: 'cancelled',
    REFUNDED: 'refunded'
}

// 订单状态映射
const orderStatusMap = {
    [OrderStatusEnum.PENDING]: '待支付',
    [OrderStatusEnum.PAID]: '已支付',
    [OrderStatusEnum.CANCELLED]: '已取消',
    [OrderStatusEnum.REFUNDED]: '已退款'
}

// 状态颜色映射
const statusColorMap = {
    [OrderStatusEnum.PENDING]: 'warning',
    [OrderStatusEnum.PAID]: 'success',
    [OrderStatusEnum.CANCELLED]: 'default',
    [OrderStatusEnum.REFUNDED]: 'error'
}

const getStatusText = (status) => {
    return orderStatusMap[status] || status
}

const getStatusColor = (status) => {
    return statusColorMap[status] || 'default'
}

const columns = [
    {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 50
    },
    {
        title: '邮箱',
        dataIndex: 'email',
        key: 'email',
        width: 150
    },
    {
        title: '订单号',
        dataIndex: 'order_no',
        key: 'order_no',
        width: 200,
        ellipsis: true
    },
    {
        title: '会员计划',
        key: 'plan_info',
        width: 80
    },
    {
        title: '金额',
        key: 'amount',
        width: 140
    },
    {
        title: '订单状态',
        dataIndex: 'status',
        key: 'status',
        width: 90
    },
    {
        title: '支付渠道',
        dataIndex: 'pay_channel',
        key: 'pay_channel',
        width: 100
    },
    {
        title: '支付时间',
        dataIndex: 'pay_time',
        key: 'pay_time',
        width: 180,
        customRender: ({ text }) => text ? formatDate(text) : '-'
    },
    {
        title: '创建时间',
        dataIndex: 'create_time',
        key: 'create_time',
        width: 180,
        customRender: ({ text }) => formatDate(text)
    }
];

const pagination = ref({
    total: 0,
    current: 1,
    pageSize: 20,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`
});

const params = ref({
    page: 1,
    page_size: pagination.value.pageSize
})

const data = ref([])
const loading = ref(false)

const getList = async () => {
    loading.value = true
    try {
        const res = await getMemberOrderListAPI(params.value)
        data.value = res.data
        pagination.value.total = res.total
    } catch (error) {
        console.error('获取订单列表失败:', error)
    } finally {
        loading.value = false
    }
}

const handleTableChange = (page) => {
    pagination.value.current = page.current
    params.value.page = page.current
    pagination.value.pageSize = page.pageSize
    params.value.page_size = page.pageSize
    getList()
};

onMounted(() => {
    getList()
})
</script>

<style lang="scss" scoped>
.order-list {
    padding: 24px;

    .plan-name {
        font-weight: 500;
        margin-bottom: 4px;
    }

    .plan-duration {
        color: rgba(0, 0, 0, 0.45);
        font-size: 13px;
    }

    .actual-amount {
        color: #f5222d;
        font-weight: 500;
    }
}
</style> 