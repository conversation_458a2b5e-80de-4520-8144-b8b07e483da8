# 灵象工具箱后端用户系统

## 一、数据库安装

### 1. 安装mysql

```shell
apt update 
apt install mysql-server
```

### 2. 创建需要的数据库
```sql
--  修改默认密码
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'NT28xrUP4fC2V67EUnYY';

--  创建数据库

CREATE DATABASE lxt_tools CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 二、python 环境安装

### 1、安装pyenv
```shell
git clone https://gitee.com/mirrors/pyenv.git ~/.pyenv

git clone https://gh-proxy.com/github.com/pyenv/pyenv-virtualenv.git $(pyenv root)/plugins/pyenv-virtualenv
```
### 2. vim ~/.bashrc
```shell
export PYENV_ROOT="$HOME/.pyenv"
export PATH="$PYENV_ROOT/bin:$PATH"
eval "$(pyenv init --path)"
eval "$(pyenv init -)"
eval "$(pyenv virtualenv-init -)"  # 如果使用虚拟环境插件
```
### 3. 配置加速源

```shell
export PYTHON_BUILD_MIRROR_URL="https://mirrors.huaweicloud.com/python/"
export PYTHON_BUILD_MIRROR_URL_SKIP_CHECKSUM=1
```
### 4. 安装python

```shell
sudo apt install -y libbz2-dev libncursesw5-dev libreadline-dev libsqlite3-dev liblzma-dev python3-dev libssl-dev

pyenv install 3.12.10

pyenv virtualenv 3.12.19 xb-user


pyenv activate xb-user

pip config set global.index-url  https://pypi.mirrors.ustc.edu.cn/simple/

pip install -r requirementx.txt

pip install gunicorn uvicorn[standard]
```

## 三、 安装nginx 和 supervisor

```shell
apt install -y nginx

apt install -y supervisor
```

## 四、cerbot https 自动证书配置

```shell
# 1. 安装snapd https://snapcraft.io/docs/installing-snap-on-ubuntu
sudo apt install snapd
# 2. 安装certbot  https://certbot.eff.org/instructions?ws=nginx&os=snap&tab=standard
sudo snap install --classic certbot

sudo ln -s /snap/bin/certbot /usr/bin/certbot
# 自动获取证书和配置nignx
sudo certbot --nginx

# 只获取证书，配置nginx
sudo certbot certonly --nginx -d api.xiaobinai.top
sudo certbot certonly --nginx -d xiaobinai.top


# 自动需求，并重启nginx
sudo crontab -e
# 添加下面内容
0 3 * * * certbot renew --quiet --post-hook "systemctl reload nginx"

```

## 安装node 
```shell

# Download and install nvm:
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.2/install.sh | bash

# in lieu of restarting the shell
\. "$HOME/.nvm/nvm.sh"

# Download and install Node.js:
nvm install 22

# Verify the Node.js version:
node -v # Should print "v22.15.0".
nvm current # Should print "v22.15.0".

# Verify npm version:
npm -v # Should print "10.9.2".

npm install -g pnpm@latest-10
# 加速
pnpm config set registry https://registry.npmmirror.com
```


## 定时备份数据库
 
```shell
chmod +x backup_db.sh

crontab -e

# 例如每天凌晨2点执行备份
0 2 * * * /root/LXT/XB-user/backup_db.sh

```

## 还原数据库

```shell
# 先连接到MySQL
mysql -u root -p'NT28xrUP4fC2V67EUnYY'

# 在MySQL中执行以下命令
DROP DATABASE IF EXISTS lxt_tools;
CREATE DATABASE lxt_tools;
exit;

# 然后再执行还原命令
gunzip < /backup/mysql/lxt_tools_备份日期_时间.sql.gz | mysql -u root -p'NT28xrUP4fC2V67EUnYY' lxt_tools

```